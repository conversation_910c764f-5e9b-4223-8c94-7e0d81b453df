import { useSearchCustomer } from '@/apis/customer/customer.api';
import {
    SearchCustomer,
    SearchCustomerResponse,
} from '@/apis/customer/customer.type';
import InputSearchNameWithApiControl from '@/components/common/FormController/InputSearchNameWithApiControl';
import { LeadStatus as LeadStatusData } from '@/constants/sharedData/sharedData';
import { useState } from 'react';
import { FormProvider, useForm, useWatch } from 'react-hook-form';
import {
    <PERSON>ton,
    Card,
    CardHeader,
    Col,
    Modal,
    ModalBody,
    ModalFooter,
    ModalHeader,
    Table,
    Input,
} from 'reactstrap';

interface AddCustomerModalProps {
    isOpen: boolean;
    toggle: () => void;
    onSuccess?: (selectedCustomers: SearchCustomerResponse[]) => void;
}

const AddCustomerModal = ({
    isOpen,
    toggle,
    onSuccess,
}: AddCustomerModalProps) => {
    const [selectedIds, setSelectedIds] = useState<string[]>([]);
    const [selectedData, setSelectedData] = useState<SearchCustomerResponse[]>(
        [],
    );
    const methods = useForm<SearchCustomer>({
        defaultValues: {
            PageNumber: 1,
            PageSize: 10,
            SortField: '',
            IsDescending: false,
        },
    });
    const { control, setValue } = methods;
    const [
        Name,
        IndustryId,
        BusinessType,
        LeadStatus,
        SalePerson,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    ] = useWatch({
        control,
        name: [
            'Name',
            'IndustryId',
            'BusinessType',
            'LeadStatus',
            'SalePerson',
            'FromDate',
            'ToDate',
            'PageNumber',
            'PageSize',
            'SortField',
            'IsDescending',
        ],
    });
    const { data, isLoading } = useSearchCustomer({
        Name,
        IndustryId,
        BusinessType,
        LeadStatus,
        SalePerson,
        FromDate,
        ToDate,
        PageNumber,
        PageSize,
        SortField,
        IsDescending,
    });
    const { items: listCustomer = [], totalItems = 0 } = data ?? {};
    const formattedCustomer = listCustomer.map(
        (Customer: SearchCustomerResponse) => ({
            ...Customer,
            createdOn: Customer.createdOn
                ? new Date(
                      Customer.createdOn.substring(0, 10),
                  ).toLocaleDateString('vi-VN')
                : '',
        }),
    );

    // Pagination state
    const [currentPage, setCurrentPage] = useState(1);
    const itemsPerPage = 10;

    // Calculate pagination
    const totalPages = Math.ceil(totalItems / itemsPerPage);
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const currentData = formattedCustomer.slice(startIndex, endIndex);

    // Handle checkbox selection
    const handleSelectCustomer = (customer: SearchCustomerResponse) => {
        const isSelected = selectedIds.includes(customer.id);
        if (isSelected) {
            setSelectedIds(selectedIds.filter((id) => id !== customer.id));
            setSelectedData(
                selectedData.filter((item) => item.id !== customer.id),
            );
        } else {
            setSelectedIds([...selectedIds, customer.id]);
            setSelectedData([...selectedData, customer]);
        }
    };

    // Handle select all checkbox
    const handleSelectAll = () => {
        if (
            selectedIds.length === currentData.length &&
            currentData.length > 0
        ) {
            setSelectedIds([]);
            setSelectedData([]);
        } else {
            const allIds = currentData.map((item) => item.id);
            setSelectedIds(allIds);
            setSelectedData(currentData);
        }
    };

    const handleCancel = () => {
        setSelectedIds([]);
        setSelectedData([]);
        toggle();
    };

    const handleSave = () => {
        if (onSuccess && selectedData.length > 0) {
            onSuccess(selectedData);
        }
        setSelectedIds([]);
        setSelectedData([]);
        toggle();
    };

    return (
        <Modal isOpen={isOpen} toggle={toggle} size='xl'>
            <FormProvider {...methods}>
                <ModalHeader toggle={toggle}>Thêm khách hàng</ModalHeader>
                <ModalBody>
                    <Col lg={12}>
                        <Card>
                            <CardHeader>
                                <div className='d-flex flex-wrap align-items-center gap-2'>
                                    <InputSearchNameWithApiControl
                                        name='Name'
                                        placeholder='Tìm kiếm theo tên nhóm khách hàng...'
                                    />
                                </div>
                            </CardHeader>

                            {isLoading ? (
                                <div className='text-center p-4'>
                                    <div
                                        className='spinner-border'
                                        role='status'
                                    >
                                        <span className='visually-hidden'>
                                            Loading...
                                        </span>
                                    </div>
                                </div>
                            ) : (
                                <>
                                    <Table responsive striped bordered hover>
                                        <thead>
                                            <tr>
                                                <th style={{ width: '50px' }}>
                                                    <Input
                                                        type='checkbox'
                                                        checked={
                                                            selectedIds.length ===
                                                                currentData.length &&
                                                            currentData.length >
                                                                0
                                                        }
                                                        onChange={
                                                            handleSelectAll
                                                        }
                                                    />
                                                </th>
                                                <th>Tên khách hàng</th>
                                                <th>Loại hình</th>
                                                <th>Lĩnh vực</th>
                                                <th>Giai đoạn</th>
                                                <th style={{ width: '100px' }}>
                                                    Hành động
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {currentData.length > 0 ? (
                                                currentData.map((customer) => {
                                                    const status =
                                                        LeadStatusData.find(
                                                            (s) =>
                                                                s.value ===
                                                                customer.leadStatus,
                                                        );
                                                    const isSelected =
                                                        selectedIds.includes(
                                                            customer.id,
                                                        );

                                                    return (
                                                        <tr key={customer.id}>
                                                            <td>
                                                                <Input
                                                                    type='checkbox'
                                                                    checked={
                                                                        isSelected
                                                                    }
                                                                    onChange={() =>
                                                                        handleSelectCustomer(
                                                                            customer,
                                                                        )
                                                                    }
                                                                />
                                                            </td>
                                                            <td>
                                                                {customer.name}
                                                            </td>
                                                            <td>
                                                                {
                                                                    customer.businessTypeName
                                                                }
                                                            </td>
                                                            <td>
                                                                {
                                                                    customer.industryName
                                                                }
                                                            </td>
                                                            <td>
                                                                <span
                                                                    className='badge me-1'
                                                                    style={{
                                                                        backgroundColor:
                                                                            '#daf4f0',
                                                                        color: '#2fbeab',
                                                                        display:
                                                                            'inline-block',
                                                                        textAlign:
                                                                            'center',
                                                                        padding:
                                                                            '4px 8px',
                                                                        fontSize:
                                                                            '12px',
                                                                        fontWeight: 500,
                                                                        borderRadius:
                                                                            '4px',
                                                                    }}
                                                                >
                                                                    {status?.label ||
                                                                        ''}
                                                                </span>
                                                            </td>
                                                            <td>
                                                                <Button
                                                                    color='primary'
                                                                    size='sm'
                                                                    onClick={() =>
                                                                        handleSelectCustomer(
                                                                            customer,
                                                                        )
                                                                    }
                                                                >
                                                                    {isSelected
                                                                        ? 'Bỏ chọn'
                                                                        : 'Chọn'}
                                                                </Button>
                                                            </td>
                                                        </tr>
                                                    );
                                                })
                                            ) : (
                                                <tr>
                                                    <td
                                                        colSpan={6}
                                                        className='text-center'
                                                    >
                                                        Không có dữ liệu
                                                    </td>
                                                </tr>
                                            )}
                                        </tbody>
                                    </Table>

                                    {/* Pagination */}
                                    {totalPages > 1 && (
                                        <div className='d-flex justify-content-between align-items-center mt-3'>
                                            <div>
                                                Hiển thị {startIndex + 1} -{' '}
                                                {Math.min(endIndex, totalItems)}{' '}
                                                của {totalItems} kết quả
                                            </div>
                                            <div className='d-flex gap-2'>
                                                <Button
                                                    color='secondary'
                                                    size='sm'
                                                    disabled={currentPage === 1}
                                                    onClick={() => {
                                                        setCurrentPage(
                                                            currentPage - 1,
                                                        );
                                                        setValue(
                                                            'PageNumber',
                                                            currentPage - 1,
                                                        );
                                                    }}
                                                >
                                                    Trước
                                                </Button>
                                                <span className='align-self-center'>
                                                    Trang {currentPage} /{' '}
                                                    {totalPages}
                                                </span>
                                                <Button
                                                    color='secondary'
                                                    size='sm'
                                                    disabled={
                                                        currentPage ===
                                                        totalPages
                                                    }
                                                    onClick={() => {
                                                        setCurrentPage(
                                                            currentPage + 1,
                                                        );
                                                        setValue(
                                                            'PageNumber',
                                                            currentPage + 1,
                                                        );
                                                    }}
                                                >
                                                    Sau
                                                </Button>
                                            </div>
                                        </div>
                                    )}
                                </>
                            )}
                        </Card>
                    </Col>
                </ModalBody>
                <ModalFooter>
                    <Button color='danger' onClick={handleCancel}>
                        Huỷ
                    </Button>
                    <Button
                        color='success'
                        onClick={handleSave}
                        disabled={selectedData.length === 0}
                    >
                        Lưu
                    </Button>
                </ModalFooter>
            </FormProvider>
        </Modal>
    );
};

export default AddCustomerModal;
